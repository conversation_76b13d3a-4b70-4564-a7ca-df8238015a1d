"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { CreditCard, CheckCircle, Clock, Euro, User, Calendar, AlertCircle, Loader2, Plus } from "lucide-react";
import { formatAmount } from "@/lib/stripe";
import { supabase } from "@/lib/supabase";

interface PaymentManagementProps {
	reservationId: string;
}

interface PaymentStatus {
	reservation: {
		id: string;
		reservationNumber: string;
		totalAmount: number;
		customerName: string;
		serviceName: string;
		status: string;
	};
	paymentSummary: {
		totalAmount: number;
		totalPaid: number;
		remainingAmount: number;
		paymentStatus: string;
		hasDepositPayment: boolean;
		hasFullPayment: boolean;
		hasManualPayments: boolean;
		canMarkComplete: boolean;
	};
	payments: Array<{
		id: string;
		amount: number;
		currency: string;
		paymentType: string;
		isDeposit: boolean;
		paymentMethod: string;
		paymentDate: string;
		completedManually: boolean;
		completedBy: string | null;
		completionNotes: string | null;
		paymentIntentId: string | null;
	}>;
}

export function PaymentManagement({ reservationId }: PaymentManagementProps) {
	const [paymentStatus, setPaymentStatus] = useState<PaymentStatus | null>(null);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [showMarkCompleteDialog, setShowMarkCompleteDialog] = useState(false);
	const [markingComplete, setMarkingComplete] = useState(false);

	// Form state for marking payment complete
	const [paymentAmount, setPaymentAmount] = useState<string>("");
	const [paymentMethod, setPaymentMethod] = useState<string>("cash");
	const [notes, setNotes] = useState<string>("");

	useEffect(() => {
		loadPaymentStatus();
	}, [reservationId]);

	const loadPaymentStatus = async () => {
		try {
			setLoading(true);
			setError(null);

			// Get current session for auth
			const {
				data: { session },
			} = await supabase.auth.getSession();

			if (!session?.access_token) {
				throw new Error("No authentication session found");
			}

			const response = await fetch(`/api/admin/reservations/${reservationId}/payment-status`, {
				headers: {
					Authorization: `Bearer ${session.access_token}`,
					"Content-Type": "application/json",
				},
			});

			const result = await response.json();

			if (!response.ok) {
				throw new Error(result.error || "Failed to load payment status");
			}

			setPaymentStatus(result);

			// Set default payment amount to remaining amount
			if (result.paymentSummary.remainingAmount > 0) {
				setPaymentAmount(result.paymentSummary.remainingAmount.toString());
			}
		} catch (err) {
			console.error("Error loading payment status:", err);
			setError(err instanceof Error ? err.message : "Failed to load payment status");
		} finally {
			setLoading(false);
		}
	};

	const handleMarkComplete = async () => {
		if (!paymentStatus || !paymentAmount) return;

		try {
			setMarkingComplete(true);
			setError(null);

			// Get current session for auth
			const {
				data: { session },
			} = await supabase.auth.getSession();

			if (!session?.access_token) {
				throw new Error("No authentication session found");
			}

			const response = await fetch("/api/admin/payments/mark-complete", {
				method: "POST",
				headers: {
					Authorization: `Bearer ${session.access_token}`,
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					reservationId,
					amount: parseFloat(paymentAmount),
					paymentMethod,
					notes: notes.trim() || undefined,
				}),
			});

			const result = await response.json();

			if (!response.ok) {
				throw new Error(result.error || "Failed to mark payment as complete");
			}

			// Reload payment status
			await loadPaymentStatus();

			// Reset form and close dialog
			setPaymentAmount("");
			setPaymentMethod("cash");
			setNotes("");
			setShowMarkCompleteDialog(false);
		} catch (err) {
			console.error("Error marking payment complete:", err);
			setError(err instanceof Error ? err.message : "Failed to mark payment as complete");
		} finally {
			setMarkingComplete(false);
		}
	};

	const getPaymentStatusBadge = (status: string) => {
		switch (status) {
			case "fully_paid":
				return (
					<Badge className="bg-emerald-100 text-emerald-800">
						<CheckCircle className="w-3 h-3 mr-1" />
						Soldé
					</Badge>
				);
			case "deposit_paid":
				return (
					<Badge className="bg-orange-100 text-orange-800">
						<Clock className="w-3 h-3 mr-1" />
						Acompte payé
					</Badge>
				);
			case "partial_paid":
				return (
					<Badge className="bg-blue-100 text-blue-800">
						<CreditCard className="w-3 h-3 mr-1" />
						Partiellement payé
					</Badge>
				);
			default:
				return (
					<Badge variant="outline">
						<AlertCircle className="w-3 h-3 mr-1" />
						Non payé
					</Badge>
				);
		}
	};

	const getPaymentMethodLabel = (method: string) => {
		switch (method) {
			case "card":
				return "Carte bancaire (TPE)";
			case "stripe":
				return "Stripe";
			case "cash":
				return "Espèces";
			case "bank_transfer":
				return "Virement";
			case "check":
				return "Chèque";
			case "paypal":
				return "PayPal";
			default:
				return method;
		}
	};

	if (loading) {
		return (
			<Card>
				<CardContent className="p-6">
					<div className="flex items-center justify-center">
						<Loader2 className="w-6 h-6 animate-spin text-emerald-500 mr-3" />
						<span className="text-gray-600">Chargement du statut de paiement...</span>
					</div>
				</CardContent>
			</Card>
		);
	}

	if (error || !paymentStatus) {
		return (
			<Card>
				<CardContent className="p-6">
					<Alert variant="destructive">
						<AlertCircle className="h-4 w-4" />
						<AlertDescription>{error || "Impossible de charger le statut de paiement"}</AlertDescription>
					</Alert>
				</CardContent>
			</Card>
		);
	}

	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center justify-between">
					<div className="flex items-center gap-2">
						<CreditCard className="w-5 h-5" />
						Gestion des paiements
					</div>
					{getPaymentStatusBadge(paymentStatus.paymentSummary.paymentStatus)}
				</CardTitle>
			</CardHeader>
			<CardContent className="space-y-6">
				{/* Payment Summary */}
				<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
					<div className="text-center p-4 bg-gray-50 rounded-lg">
						<div className="text-2xl font-bold text-gray-900">
							{formatAmount(paymentStatus.paymentSummary.totalAmount * 100)}
						</div>
						<div className="text-sm text-gray-600">Montant total</div>
					</div>
					<div className="text-center p-4 bg-emerald-50 rounded-lg">
						<div className="text-2xl font-bold text-emerald-600">
							{formatAmount(paymentStatus.paymentSummary.totalPaid * 100)}
						</div>
						<div className="text-sm text-gray-600">Montant payé</div>
					</div>
					<div className="text-center p-4 bg-orange-50 rounded-lg">
						<div className="text-2xl font-bold text-orange-600">
							{formatAmount(paymentStatus.paymentSummary.remainingAmount * 100)}
						</div>
						<div className="text-sm text-gray-600">Solde restant</div>
					</div>
				</div>

				{/* Mark as Complete Button */}
				{paymentStatus.paymentSummary.canMarkComplete && paymentStatus.paymentSummary.remainingAmount > 0 && (
					<div className="flex justify-center">
						<Dialog open={showMarkCompleteDialog} onOpenChange={setShowMarkCompleteDialog}>
							<DialogTrigger asChild>
								<Button className="bg-emerald-500 hover:bg-emerald-600">
									<Plus className="w-4 h-4 mr-2" />
									Marquer comme payé
								</Button>
							</DialogTrigger>
							<DialogContent>
								<DialogHeader>
									<DialogTitle>Marquer le paiement comme terminé</DialogTitle>
								</DialogHeader>
								<div className="space-y-4">
									<div className="space-y-2">
										<Label htmlFor="amount">Montant payé</Label>
										<div className="relative">
											<Input
												id="amount"
												type="number"
												step="0.01"
												min="0"
												max={paymentStatus.paymentSummary.remainingAmount}
												value={paymentAmount}
												onChange={(e) => setPaymentAmount(e.target.value)}
												className="pl-8"
											/>
											<Euro className="absolute left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
										</div>
									</div>

									<div className="space-y-2">
										<Label htmlFor="method">Méthode de paiement</Label>
										<Select value={paymentMethod} onValueChange={setPaymentMethod}>
											<SelectTrigger>
												<SelectValue />
											</SelectTrigger>
											<SelectContent>
												<SelectItem value="cash">Espèces</SelectItem>
												<SelectItem value="card">Carte bancaire (TPE)</SelectItem>
												<SelectItem value="bank_transfer">Virement bancaire</SelectItem>
												<SelectItem value="check">Chèque</SelectItem>
												<SelectItem value="stripe">Stripe</SelectItem>
												<SelectItem value="paypal">PayPal</SelectItem>
											</SelectContent>
										</Select>
									</div>

									<div className="space-y-2">
										<Label htmlFor="notes">Notes (optionnel)</Label>
										<Textarea
											id="notes"
											value={notes}
											onChange={(e) => setNotes(e.target.value)}
											placeholder="Informations complémentaires sur le paiement..."
											rows={3}
										/>
									</div>

									<div className="flex justify-end space-x-2">
										<Button
											variant="outline"
											onClick={() => setShowMarkCompleteDialog(false)}
											disabled={markingComplete}
										>
											Annuler
										</Button>
										<Button
											onClick={handleMarkComplete}
											disabled={markingComplete || !paymentAmount}
											className="bg-emerald-500 hover:bg-emerald-600"
										>
											{markingComplete ? (
												<>
													<Loader2 className="w-4 h-4 mr-2 animate-spin" />
													Enregistrement...
												</>
											) : (
												"Confirmer le paiement"
											)}
										</Button>
									</div>
								</div>
							</DialogContent>
						</Dialog>
					</div>
				)}

				<Separator />

				{/* Payment History */}
				<div>
					<h3 className="font-semibold mb-4">Historique des paiements</h3>
					{paymentStatus.payments.length === 0 ? (
						<div className="text-center text-gray-500 py-8">Aucun paiement enregistré</div>
					) : (
						<div className="space-y-3">
							{paymentStatus.payments.map((payment) => (
								<div key={payment.id} className="border rounded-lg p-4">
									<div className="flex justify-between items-start">
										<div className="space-y-1">
											<div className="flex items-center gap-2">
												<span className="font-medium">
													{formatAmount(payment.amount * 100)}
												</span>
												{payment.isDeposit && (
													<Badge variant="secondary" className="text-xs">
														Acompte
													</Badge>
												)}
												{payment.completedManually && (
													<Badge variant="outline" className="text-xs">
														Manuel
													</Badge>
												)}
											</div>
											<div className="text-sm text-gray-600">
												{getPaymentMethodLabel(payment.paymentMethod)} •{" "}
												{new Date(payment.paymentDate).toLocaleDateString("fr-FR")}
											</div>
											{payment.completedBy && (
												<div className="text-xs text-gray-500 flex items-center gap-1">
													<User className="w-3 h-3" />
													Traité par {payment.completedBy}
												</div>
											)}
											{payment.completionNotes && (
												<div className="text-xs text-gray-600 italic">
													"{payment.completionNotes}"
												</div>
											)}
										</div>
										<CheckCircle className="w-5 h-5 text-emerald-500" />
									</div>
								</div>
							))}
						</div>
					)}
				</div>
			</CardContent>
		</Card>
	);
}
