"use client";

import { useState, useEffect } from "react";
import { Mail, Send, CheckCircle, XCircle, AlertCircle, Loader2 } from "lucide-react";
import Button from "./ui/Button";
import { supabase } from "@/lib/supabase";

interface EmailActionsProps {
	reservation: {
		id: string;
		reservation_number: string;
		status: string;
		customer?: {
			email?: string;
			first_name?: string;
			last_name?: string;
		};
		payments?: Array<{
			id: string;
			status: string;
			is_deposit: boolean;
			amount: number;
			payment_date?: string;
		}>;
	};
}

interface EmailResult {
	success: boolean;
	messageId?: string;
	error?: string;
	emailType: string;
	recipient: string;
}

const EmailActions: React.FC<EmailActionsProps> = ({ reservation }) => {
	const [sendingEmail, setSendingEmail] = useState<string | null>(null);
	const [emailResults, setEmailResults] = useState<Record<string, EmailResult>>({});
	const [showConfirmDialog, setShowConfirmDialog] = useState<{
		emailType: string;
		paymentId?: string;
	} | null>(null);
	const [adminEmail, setAdminEmail] = useState<string>("<EMAIL>");
	const [completedPayments, setCompletedPayments] = useState<
		Array<{
			id: string;
			amount: number;
			currency: string;
			paymentType: string;
			isDeposit: boolean;
			paymentMethod: string;
			paymentDate: string;
			completedManually: boolean;
			completedBy: string | null;
			completionNotes: string | null;
			paymentIntentId: string | null;
		}>
	>([]);
	const [loadingPayments, setLoadingPayments] = useState(true);

	const customerEmail = reservation.customer?.email;
	const customerName = reservation.customer
		? `${reservation.customer.first_name} ${reservation.customer.last_name}`.trim()
		: "Client inconnu";

	// Fetch admin notification email
	useEffect(() => {
		const fetchAdminEmail = async () => {
			try {
				const {
					data: { session },
				} = await supabase.auth.getSession();
				if (!session?.access_token) return;

				const response = await fetch("/api/admin/settings/admin-email", {
					headers: {
						Authorization: `Bearer ${session.access_token}`,
					},
				});

				if (response.ok) {
					const result = await response.json();
					if (result.success && result.adminEmail) {
						setAdminEmail(result.adminEmail);
					}
				}
			} catch (error) {
				console.error("Error fetching admin email:", error);
				// Keep default email
			}
		};

		fetchAdminEmail();
	}, []);

	// Fetch payment data for this reservation
	useEffect(() => {
		const fetchPayments = async () => {
			try {
				setLoadingPayments(true);
				const {
					data: { session },
				} = await supabase.auth.getSession();
				if (!session?.access_token) return;

				const response = await fetch(`/api/admin/reservations/${reservation.id}/payment-status`, {
					headers: {
						Authorization: `Bearer ${session.access_token}`,
					},
				});

				if (response.ok) {
					const result = await response.json();
					console.log("Payment status API response:", result);
					if (result.success && result.payments) {
						console.log("Setting completed payments:", result.payments);
						setCompletedPayments(result.payments);
					}
				}
			} catch (error) {
				console.error("Error fetching payments:", error);
			} finally {
				setLoadingPayments(false);
			}
		};

		fetchPayments();
	}, [reservation.id]);

	// Get the correct recipient email based on email type
	const getRecipientEmail = (emailType: string) => {
		if (emailType === "admin_new_reservation" || emailType === "admin_payment_received") {
			return adminEmail;
		}
		return customerEmail;
	};

	// Get the correct recipient display name based on email type
	const getRecipientDisplay = (emailType: string) => {
		if (emailType === "admin_new_reservation" || emailType === "admin_payment_received") {
			return `${adminEmail} (Admin)`;
		}
		return `${customerEmail} (${customerName})`;
	};

	const sendEmail = async (emailType: string, paymentId?: string) => {
		const recipientEmail = getRecipientEmail(emailType);
		if (!recipientEmail) {
			alert("Aucune adresse email trouvée pour ce destinataire");
			return;
		}

		setSendingEmail(emailType + (paymentId ? `-${paymentId}` : ""));
		setShowConfirmDialog(null);

		try {
			const {
				data: { session },
			} = await supabase.auth.getSession();

			if (!session?.access_token) {
				throw new Error("Session d'authentification non trouvée");
			}

			const response = await fetch(`/api/admin/reservations/${reservation.id}/send-email`, {
				method: "POST",
				headers: {
					Authorization: `Bearer ${session.access_token}`,
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					emailType,
					paymentId,
				}),
			});

			const result = await response.json();

			if (!response.ok) {
				throw new Error(result.error || "Échec de l'envoi de l'email");
			}

			// Store result for display
			const resultKey = emailType + (paymentId ? `-${paymentId}` : "");
			setEmailResults((prev) => ({
				...prev,
				[resultKey]: result,
			}));

			// Clear result after 5 seconds
			setTimeout(() => {
				setEmailResults((prev) => {
					const newResults = { ...prev };
					delete newResults[resultKey];
					return newResults;
				});
			}, 5000);
		} catch (error) {
			console.error("Erreur lors de l'envoi de l'email:", error);
			const resultKey = emailType + (paymentId ? `-${paymentId}` : "");
			setEmailResults((prev) => ({
				...prev,
				[resultKey]: {
					success: false,
					error: error instanceof Error ? error.message : "Erreur inconnue",
					emailType,
					recipient: recipientEmail,
				},
			}));

			// Clear error after 5 seconds
			setTimeout(() => {
				setEmailResults((prev) => {
					const newResults = { ...prev };
					delete newResults[resultKey];
					return newResults;
				});
			}, 5000);
		} finally {
			setSendingEmail(null);
		}
	};

	const handleEmailClick = (emailType: string, paymentId?: string) => {
		setShowConfirmDialog({ emailType, paymentId });
	};

	const getEmailTypeLabel = (emailType: string) => {
		switch (emailType) {
			case "booking_confirmation":
				return "Confirmation de réservation";
			case "payment_confirmation":
				return "Confirmation de paiement";
			case "booking_reminder":
				return "Rappel de réservation";
			case "admin_new_reservation":
				return "Notification admin - Nouvelle réservation";
			case "admin_payment_received":
				return "Notification admin - Paiement reçu";
			default:
				return emailType;
		}
	};

	const getEmailTypeDescription = (emailType: string) => {
		switch (emailType) {
			case "booking_confirmation":
				return "Email avec PDF de confirmation et QR code";
			case "payment_confirmation":
				return "Email avec facture PDF";
			case "booking_reminder":
				return "Email de rappel 24h avant";
			case "admin_new_reservation":
				return "Notification interne nouvelle réservation";
			case "admin_payment_received":
				return "Notification interne paiement reçu";
			default:
				return "";
		}
	};

	const getResultIcon = (result: EmailResult) => {
		if (result.success) {
			return <CheckCircle className="h-4 w-4 text-green-600" />;
		} else {
			return <XCircle className="h-4 w-4 text-red-600" />;
		}
	};

	if (!customerEmail && !adminEmail) {
		return (
			<div className="bg-gray-50 rounded-lg p-4">
				<div className="flex items-center gap-2 text-gray-600">
					<AlertCircle className="h-4 w-4" />
					<span className="text-sm">Aucune adresse email disponible</span>
				</div>
			</div>
		);
	}

	return (
		<div>
			<h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
				<Mail className="h-5 w-5" />
				Actions Email
			</h3>

			<div className="bg-gray-50 rounded-lg p-4 space-y-4">
				{/* Customer Emails */}
				{customerEmail && (
					<div>
						<h4 className="font-medium text-gray-900 mb-2">Emails client</h4>
						<div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
							<Button
								onClick={() => handleEmailClick("booking_confirmation")}
								disabled={sendingEmail !== null}
								className="flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 text-sm py-2"
							>
								{sendingEmail === "booking_confirmation" ? (
									<Loader2 className="h-4 w-4 animate-spin" />
								) : (
									<Send className="h-4 w-4" />
								)}
								Confirmation réservation
							</Button>

							<Button
								onClick={() => handleEmailClick("booking_reminder")}
								disabled={sendingEmail !== null}
								className="flex items-center justify-center gap-2 bg-orange-600 hover:bg-orange-700 text-sm py-2"
							>
								{sendingEmail === "booking_reminder" ? (
									<Loader2 className="h-4 w-4 animate-spin" />
								) : (
									<Send className="h-4 w-4" />
								)}
								Rappel réservation
							</Button>
						</div>
					</div>
				)}

				{/* Payment Confirmation Emails */}
				{customerEmail && (
					<div>
						<h4 className="font-medium text-gray-900 mb-2">Confirmations de paiement</h4>
						{loadingPayments ? (
							<div className="flex items-center gap-2 text-gray-600 text-sm">
								<Loader2 className="h-4 w-4 animate-spin" />
								<span>Chargement des paiements...</span>
							</div>
						) : completedPayments.length > 0 ? (
							<div className="space-y-2">
								{completedPayments.map((payment) => (
									<Button
										key={payment.id}
										onClick={() => handleEmailClick("payment_confirmation", payment.id)}
										disabled={sendingEmail !== null}
										className="flex items-center justify-between w-full bg-green-600 hover:bg-green-700 text-sm py-2"
									>
										<div className="flex items-center gap-2">
											{sendingEmail === `payment_confirmation-${payment.id}` ? (
												<Loader2 className="h-4 w-4 animate-spin" />
											) : (
												<Send className="h-4 w-4" />
											)}
											<span>
												{payment.isDeposit ? "Acompte" : "Paiement complet"} - €
												{payment.amount.toFixed(2)}
											</span>
										</div>
										<span className="text-xs opacity-75">
											{payment.paymentDate
												? new Date(payment.paymentDate).toLocaleDateString("fr-FR")
												: "Date inconnue"}
										</span>
									</Button>
								))}
							</div>
						) : (
							<div className="text-sm text-gray-500">Aucun paiement complété trouvé</div>
						)}
					</div>
				)}

				{/* Message when customer email is not available */}
				{!customerEmail && (
					<div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
						<div className="flex items-start gap-2">
							<AlertCircle className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
							<div className="text-sm text-yellow-800">
								<p className="font-medium">Email client non disponible</p>
								<p>
									Les emails client ne peuvent pas être envoyés car aucune adresse email n'est
									configurée pour ce client.
								</p>
							</div>
						</div>
					</div>
				)}

				{/* Admin Notification Emails */}
				<div>
					<h4 className="font-medium text-gray-900 mb-2">Notifications admin</h4>
					<div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
						<Button
							onClick={() => handleEmailClick("admin_new_reservation")}
							disabled={sendingEmail !== null}
							className="flex items-center justify-center gap-2 bg-purple-600 hover:bg-purple-700 text-sm py-2"
						>
							{sendingEmail === "admin_new_reservation" ? (
								<Loader2 className="h-4 w-4 animate-spin" />
							) : (
								<Send className="h-4 w-4" />
							)}
							Nouvelle réservation
						</Button>

						{!loadingPayments && completedPayments.length > 0 && (
							<div className="space-y-1">
								{completedPayments.map((payment) => (
									<Button
										key={`admin-${payment.id}`}
										onClick={() => handleEmailClick("admin_payment_received", payment.id)}
										disabled={sendingEmail !== null}
										className="flex items-center justify-center gap-2 bg-purple-600 hover:bg-purple-700 text-sm py-2 w-full"
									>
										{sendingEmail === `admin_payment_received-${payment.id}` ? (
											<Loader2 className="h-4 w-4 animate-spin" />
										) : (
											<Send className="h-4 w-4" />
										)}
										Paiement reçu
									</Button>
								))}
							</div>
						)}
					</div>
				</div>

				{/* Email Results */}
				{Object.entries(emailResults).map(([key, result]) => (
					<div
						key={key}
						className={`flex items-center gap-2 p-2 rounded text-sm ${
							result.success ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
						}`}
					>
						{getResultIcon(result)}
						<span>
							{result.success
								? `${getEmailTypeLabel(result.emailType)} envoyé avec succès`
								: `Échec: ${result.error}`}
						</span>
					</div>
				))}
			</div>

			{/* Confirmation Dialog */}
			{showConfirmDialog && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
					<div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
						<h3 className="text-lg font-semibold text-gray-900 mb-4">Confirmer l'envoi d'email</h3>
						<div className="space-y-3 mb-6">
							<div>
								<span className="text-sm font-medium text-gray-700">Type d'email:</span>
								<p className="text-gray-900">{getEmailTypeLabel(showConfirmDialog.emailType)}</p>
								<p className="text-sm text-gray-500">
									{getEmailTypeDescription(showConfirmDialog.emailType)}
								</p>
							</div>
							<div>
								<span className="text-sm font-medium text-gray-700">Destinataire:</span>
								<p className="text-gray-900">{getRecipientDisplay(showConfirmDialog.emailType)}</p>
							</div>
							<div>
								<span className="text-sm font-medium text-gray-700">Réservation:</span>
								<p className="text-gray-900">{reservation.reservation_number}</p>
							</div>
						</div>
						<div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-6">
							<div className="flex items-start gap-2">
								<AlertCircle className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
								<div className="text-sm text-yellow-800">
									<p className="font-medium">Attention</p>
									<p>
										Cet email sera envoyé immédiatement. Assurez-vous que les informations sont
										correctes.
									</p>
								</div>
							</div>
						</div>
						<div className="flex gap-3 justify-end">
							<Button
								onClick={() => setShowConfirmDialog(null)}
								className="bg-gray-500 hover:bg-gray-600"
							>
								Annuler
							</Button>
							<Button
								onClick={() => sendEmail(showConfirmDialog.emailType, showConfirmDialog.paymentId)}
								className="bg-blue-600 hover:bg-blue-700"
							>
								Envoyer l'email
							</Button>
						</div>
					</div>
				</div>
			)}
		</div>
	);
};

export default EmailActions;
